// 用户相关API接口

import { authFetch, apiRequest, apiRequestFull, AuthManager } from './auth'

// 基础配置
const API_BASE_URL = 'http://localhost:8080/api/user'

// 类型定义
export interface RegisterRequest {
  username: string
  password: string
  email: string
  verificationCode: string
}

export interface LoginRequest {
  emailOrPhone: string
  password: string
  rememberMe?: boolean
}

export interface EmailCodeLoginRequest {
  email: string
  verificationCode: string
  rememberMe?: boolean
}

export interface ResetPasswordRequest {
  email: string
  verificationCode: string
  newPassword: string
}

export interface UpdateProfileRequest {
  realName?: string
  phone?: string
  gender?: number | null
  birthday?: string
  age?: number | null
  height?: number | null
  weight?: number | null
  allergies?: string
  dietaryPreferences?: string
  healthGoals?: string
  bio?: string
}

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  status: number
  createTime: string
  // 个人资料扩展字段
  realName?: string
  phone?: string
  gender?: number | null
  genderName?: string
  birthday?: string
  age?: number | null
  height?: number | null
  weight?: number | null
  allergies?: string
  dietaryPreferences?: string
  healthGoals?: string
  bio?: string
}

export interface ApiResponse<T> {
  code: number
  message: string
  data?: T
  success: boolean
}



// 用户注册
export async function register(data: RegisterRequest): Promise<ApiResponse<User>> {
  const response = await fetch(`${API_BASE_URL}/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  return response.json()
}

// 用户登录
export async function login(data: LoginRequest): Promise<ApiResponse<User>> {
  const response = await fetch(`${API_BASE_URL}/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const result = await response.json()

  // 如果登录成功，保存token和用户信息
  if (result.success && result.data) {
    const { token, ...userData } = result.data
    if (token) {
      AuthManager.login(token, { ...userData, userType: 'USER' })
    }
  }

  return result
}

// 邮箱验证码登录
export async function loginWithEmailCode(data: EmailCodeLoginRequest): Promise<ApiResponse<User>> {
  const response = await fetch(`${API_BASE_URL}/login-with-code`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const result = await response.json()

  // 如果登录成功，保存token和用户信息
  if (result.success && result.data) {
    const { token, ...userData } = result.data
    if (token) {
      AuthManager.login(token, { ...userData, userType: 'USER' })
    }
  }

  return result
}

// 检查用户名是否可用（无需认证）
export async function checkUsername(username: string): Promise<ApiResponse<boolean>> {
  const response = await fetch(`${API_BASE_URL}/check-username?username=${encodeURIComponent(username)}`)
  return response.json()
}

// 检查邮箱是否可用（无需认证）
export async function checkEmail(email: string): Promise<ApiResponse<boolean>> {
  const response = await fetch(`${API_BASE_URL}/check-email?email=${encodeURIComponent(email)}`)
  return response.json()
}

// 发送邮箱验证码
export async function sendVerificationCode(email: string, type: string = 'REGISTER'): Promise<ApiResponse<void>> {
  const response = await fetch(`${API_BASE_URL}/send-verification-code`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `email=${encodeURIComponent(email)}&type=${encodeURIComponent(type)}`,
  })
  return response.json()
}

// 验证邮箱验证码
export async function verifyCode(email: string, code: string, type: string = 'REGISTER'): Promise<ApiResponse<void>> {
  const response = await fetch(`${API_BASE_URL}/verify-code`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `email=${encodeURIComponent(email)}&code=${encodeURIComponent(code)}&type=${encodeURIComponent(type)}`,
  })
  return response.json()
}

// 重置密码
export async function resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<void>> {
  const response = await fetch(`${API_BASE_URL}/reset-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  return response.json()
}

// 检查验证码是否有效（不消费验证码）
export async function checkVerificationCode(email: string, code: string, type: string = 'REGISTER'): Promise<ApiResponse<boolean>> {
  const response = await fetch(`${API_BASE_URL}/check-verification-code?email=${encodeURIComponent(email)}&code=${encodeURIComponent(code)}&type=${encodeURIComponent(type)}`)
  return response.json()
}



// 获取用户信息（需要认证）
export async function getUserInfo(): Promise<ApiResponse<User>> {
  return apiRequest<ApiResponse<User>>(`${API_BASE_URL}/info`)
}

// 更新用户信息（需要认证）
export async function updateUserInfo(data: Partial<User>): Promise<ApiResponse<User>> {
  return apiRequest<ApiResponse<User>>(`${API_BASE_URL}/update`, {
    method: 'PUT',
    body: JSON.stringify(data),
  })
}

// 用户登出
export async function logout(): Promise<void> {
  AuthManager.logout()
}

// 根据ID获取用户信息
export async function getUserById(id: number): Promise<ApiResponse<User>> {
  return apiRequest<ApiResponse<User>>(`${API_BASE_URL}/${id}`)
}

// 上传用户头像
export async function uploadUserAvatar(file: File): Promise<ApiResponse<string>> {
  const formData = new FormData()
  formData.append('file', file)

  const response = await authFetch('http://localhost:8080/api/files/avatar/user', {
    method: 'POST',
    body: formData
  })

  return response.json()
}

// 删除用户头像
export async function deleteUserAvatar(): Promise<ApiResponse<void>> {
  const response = await authFetch('http://localhost:8080/api/files/avatar', {
    method: 'DELETE'
  })

  return response.json()
}

// 获取当前用户资料信息
export async function getCurrentUserProfile(): Promise<ApiResponse<User>> {
  const response = await authFetch(`${API_BASE_URL}/profile`)
  const result = await response.json()
  return result
}

// 更新用户资料
export async function updateUserProfile(data: UpdateProfileRequest): Promise<ApiResponse<User>> {
  return apiRequestFull<ApiResponse<User>>(`${API_BASE_URL}/profile`, {
    method: 'PUT',
    body: JSON.stringify(data),
  })
}

// 获取头像URL
export function getAvatarUrl(avatarPath: string | null | undefined): string | null {
  if (!avatarPath) return null

  // 如果已经是完整URL，直接返回
  if (avatarPath.startsWith('http')) {
    return avatarPath
  }

  // 如果已经是API路径格式，直接拼接域名
  if (avatarPath.startsWith('/api/files/')) {
    return `http://localhost:8080${avatarPath}`
  }

  // 处理Windows本地路径（从后端上传的文件）
  if (avatarPath.includes('\\uploads\\avatars\\')) {
    const fileName = avatarPath.split('\\').pop()
    return `http://localhost:8080/api/files/avatars/${fileName}`
  }

  // 兼容旧格式：构建完整的头像URL
  return `http://localhost:8080/api/files/${avatarPath}`
}
