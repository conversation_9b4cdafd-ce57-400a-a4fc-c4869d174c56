<template>
  <div class="profile-container">
    <!-- 顶部导航栏 -->
    <TopNavbar
      page-title="个人资料"
      back-to="/dashboard"
      back-text="返回仪表盘"
      :show-user-info="false"
    >
      <template #actions>
        <button @click="handleSave" :disabled="isSaving" class="save-btn">
          <span v-if="isSaving">保存中...</span>
          <span v-else>保存</span>
        </button>
      </template>
    </TopNavbar>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
    </div>

    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="profile-grid">
        <!-- 头像和基本信息卡片 -->
        <div class="profile-card avatar-card">
          <h2 class="card-title">头像和基本信息</h2>
          <div class="avatar-section">
            <div class="avatar-container" @click="triggerAvatarUpload">
              <AvatarDisplay
                :avatar-url="getAvatarUrl(user?.avatar)"
                :name="user?.username"
                :size="120"
                :clickable="false"
                :show-upload-overlay="true"
              />
              <div class="avatar-overlay">
                <span class="upload-text">点击更换头像</span>
              </div>
            </div>
            <div class="basic-info">
              <div class="form-group">
                <label>用户名</label>
                <input type="text" :value="userProfile?.username" disabled class="form-input disabled" />
              </div>
              <div class="form-group">
                <label>邮箱</label>
                <input type="email" :value="userProfile?.email" disabled class="form-input disabled" />
              </div>
              <div class="form-group">
                <label>真实姓名</label>
                <input
                  type="text"
                  v-model="formData.realName"
                  placeholder="请输入真实姓名"
                  class="form-input"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 联系信息卡片 -->
        <div class="profile-card contact-card">
          <h2 class="card-title">联系信息</h2>
          <div class="form-grid">
            <div class="form-group">
              <label>手机号码</label>
              <input
                type="tel"
                v-model="formData.phone"
                placeholder="请输入手机号码"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>性别</label>
              <select v-model="formData.gender" class="form-select">
                <option :value="null">请选择性别</option>
                <option :value="1">男</option>
                <option :value="2">女</option>
                <option :value="0">其他</option>
              </select>
            </div>
            <div class="form-group">
              <label>生日</label>
              <input
                type="date"
                v-model="formData.birthday"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>年龄</label>
              <input
                type="number"
                v-model="formData.age"
                placeholder="请输入年龄"
                min="1"
                max="150"
                class="form-input"
              />
            </div>
          </div>
        </div>

        <!-- 身体数据卡片 -->
        <div class="profile-card body-card">
          <h2 class="card-title">身体数据</h2>
          <div class="form-grid">
            <div class="form-group">
              <label>身高 (cm)</label>
              <input
                type="number"
                v-model="formData.height"
                placeholder="请输入身高"
                min="50"
                max="300"
                step="0.1"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>体重 (kg)</label>
              <input
                type="number"
                v-model="formData.weight"
                placeholder="请输入体重"
                min="20"
                max="500"
                step="0.1"
                class="form-input"
              />
            </div>
            <div class="form-group bmi-display" v-if="bmi">
              <label>BMI指数</label>
              <div class="bmi-value" :class="bmiCategory.class">
                {{ bmi.toFixed(1) }} - {{ bmiCategory.text }}
              </div>
            </div>
          </div>
        </div>

        <!-- 健康信息卡片 -->
        <div class="profile-card health-card">
          <h2 class="card-title">健康信息</h2>
          <div class="form-group">
            <label>过敏信息</label>
            <div class="tag-input-container">
              <div class="tags">
                <span
                  v-for="(allergy, index) in allergiesList"
                  :key="index"
                  class="tag"
                >
                  {{ allergy }}
                  <button @click="removeAllergy(index)" class="tag-remove">×</button>
                </span>
                <span v-if="allergiesList.length === 0" class="tag-placeholder">
                  暂无过敏信息
                </span>
              </div>
              <input
                type="text"
                v-model="newAllergy"
                @keydown.enter="addAllergy"
                placeholder="输入过敏信息后按回车添加"
                class="form-input tag-input"
              />
            </div>
          </div>

          <div class="form-group">
            <label>饮食偏好</label>
            <div class="checkbox-group">
              <label class="checkbox-item" v-for="preference in dietaryOptions" :key="preference.value">
                <input
                  type="checkbox"
                  :value="preference.value"
                  v-model="selectedDietaryPreferences"
                />
                <span class="checkbox-label">{{ preference.label }}</span>
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>健康目标</label>
            <div class="checkbox-group">
              <label class="checkbox-item" v-for="goal in healthGoalOptions" :key="goal.value">
                <input
                  type="checkbox"
                  :value="goal.value"
                  v-model="selectedHealthGoals"
                />
                <span class="checkbox-label">{{ goal.label }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 个人简介卡片 -->
        <div class="profile-card bio-card">
          <h2 class="card-title">个人简介</h2>
          <div class="form-group">
            <label>关于我</label>
            <textarea
              v-model="formData.bio"
              placeholder="介绍一下自己..."
              rows="4"
              maxlength="500"
              class="form-textarea"
            ></textarea>
            <div class="char-count">{{ (formData.bio || '').length }}/500</div>
          </div>
        </div>
      </div>
    </main>

    <!-- 隐藏的头像上传文件输入 -->
    <input
      ref="avatarFileInput"
      type="file"
      accept="image/jpeg,image/jpg,image/png,image/gif"
      style="display: none"
      @change="handleAvatarFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import TopNavbar from '../components/TopNavbar.vue'
import AvatarDisplay from '../components/AvatarDisplay.vue'
import {
  getCurrentUserProfile,
  updateUserProfile,
  uploadUserAvatar,
  getAvatarUrl,
  type UpdateProfileRequest,
  type User
} from '../utils/userApi'
import { UserManager } from '../utils/auth'
import { message } from '../utils/message'

const router = useRouter()
const { user, isLoggedIn, updateUserState } = useAuth()
const avatarFileInput = ref<HTMLInputElement>()

// 用户资料数据
const userProfile = ref<User | null>(null)
const isSaving = ref(false)
const isUploading = ref(false)

// 表单数据
const formData = reactive({
  realName: '',
  phone: '',
  gender: null as number | null,
  birthday: '',
  age: null as number | null,
  height: null as number | null,
  weight: null as number | null,
  bio: ''
} as UpdateProfileRequest)

// 过敏信息管理
const newAllergy = ref('')
const allergiesList = ref<string[]>([])

// 饮食偏好和健康目标选项（从API动态获取）
const dietaryOptions = ref<Array<{value: string, label: string}>>([])
const healthGoalOptions = ref<Array<{value: string, label: string}>>([])

// 加载饮食偏好选项
const loadDietaryOptions = async () => {
  try {
    const response = await fetch('/api/dietary-preferences/enabled')
    const result = await response.json()
    if (result.success && result.data) {
      dietaryOptions.value = result.data.map((item: any) => ({
        value: item.code,
        label: item.name
      }))
    }
  } catch (error) {
    console.error('加载饮食偏好选项失败:', error)
    // 使用默认选项作为后备
    dietaryOptions.value = [
      { value: 'vegetarian', label: '素食主义' },
      { value: 'vegan', label: '纯素食' },
      { value: 'gluten-free', label: '无麸质' },
      { value: 'dairy-free', label: '无乳制品' },
      { value: 'low-carb', label: '低碳水化合物' },
      { value: 'keto', label: '生酮饮食' },
      { value: 'paleo', label: '原始人饮食' },
      { value: 'mediterranean', label: '地中海饮食' }
    ]
  }
}

// 加载健康目标选项
const loadHealthGoalOptions = async () => {
  try {
    const response = await fetch('/api/health-goals/enabled')
    const result = await response.json()
    if (result.success && result.data) {
      healthGoalOptions.value = result.data.map((item: any) => ({
        value: item.code,
        label: item.name
      }))
    }
  } catch (error) {
    console.error('加载健康目标选项失败:', error)
    // 使用默认选项作为后备
    healthGoalOptions.value = [
      { value: 'weight-loss', label: '减重' },
      { value: 'weight-gain', label: '增重' },
      { value: 'muscle-gain', label: '增肌' },
      { value: 'maintain-weight', label: '维持体重' },
      { value: 'improve-fitness', label: '提升体能' },
      { value: 'better-nutrition', label: '改善营养' },
      { value: 'manage-diabetes', label: '糖尿病管理' },
      { value: 'heart-health', label: '心脏健康' }
    ]
  }
}

const selectedDietaryPreferences = ref<string[]>([])
const selectedHealthGoals = ref<string[]>([])

// 计算BMI
const bmi = computed(() => {
  if (formData.height && formData.weight) {
    const heightInMeters = formData.height / 100
    return formData.weight / (heightInMeters * heightInMeters)
  }
  return null
})

// BMI分类
const bmiCategory = computed(() => {
  if (!bmi.value) return { text: '', class: '' }

  if (bmi.value < 18.5) {
    return { text: '偏瘦', class: 'underweight' }
  } else if (bmi.value < 24) {
    return { text: '正常', class: 'normal' }
  } else if (bmi.value < 28) {
    return { text: '超重', class: 'overweight' }
  } else {
    return { text: '肥胖', class: 'obese' }
  }
})

// 加载用户资料
const loadUserProfile = async () => {
  try {
    const response = await getCurrentUserProfile()
    if (response.success && response.data) {
      userProfile.value = response.data

      // 填充表单数据
      Object.assign(formData, {
        realName: response.data.realName || '',
        phone: response.data.phone || '',
        gender: response.data.gender,
        birthday: response.data.birthday || '',
        age: response.data.age,
        height: response.data.height,
        weight: response.data.weight,
        bio: response.data.bio || ''
      })

      // 解析过敏信息
      if (response.data.allergies) {
        try {
          allergiesList.value = JSON.parse(response.data.allergies)
        } catch {
          allergiesList.value = []
        }
      } else {
        allergiesList.value = []
      }

      // 解析饮食偏好
      if (response.data.dietaryPreferences) {
        try {
          selectedDietaryPreferences.value = JSON.parse(response.data.dietaryPreferences)
        } catch {
          selectedDietaryPreferences.value = []
        }
      } else {
        selectedDietaryPreferences.value = []
      }

      // 解析健康目标
      if (response.data.healthGoals) {
        try {
          selectedHealthGoals.value = JSON.parse(response.data.healthGoals)
        } catch {
          selectedHealthGoals.value = []
        }
      } else {
        selectedHealthGoals.value = []
      }
    } else {
      message.error(response.message || '获取用户资料失败')
    }
  } catch (error) {
    message.error('获取用户资料失败，请稍后重试')
  }
}

// 添加过敏信息
const addAllergy = () => {
  const allergy = newAllergy.value.trim()
  if (allergy && !allergiesList.value.includes(allergy)) {
    allergiesList.value.push(allergy)
    newAllergy.value = ''
  }
}

// 移除过敏信息
const removeAllergy = (index: number) => {
  allergiesList.value.splice(index, 1)
}

// 触发头像上传
const triggerAvatarUpload = () => {
  if (isUploading.value) return
  avatarFileInput.value?.click()
}

// 处理头像文件选择
const handleAvatarFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) {
    isUploading.value = false
    return
  }

  // 文件大小检查（5MB）
  if (file.size > 5 * 1024 * 1024) {
    message.error('头像文件大小不能超过5MB')
    isUploading.value = false
    return
  }

  isUploading.value = true

  try {
    const response = await uploadUserAvatar(file)
    if (response.success) {
      // 更新用户状态
      if (user.value) {
        user.value.avatar = response.data
        UserManager.setUser(user.value)
      }

      // 更新本地资料
      if (userProfile.value) {
        userProfile.value.avatar = response.data
      }

      message.success('头像上传成功')
      updateUserState()
    } else {
      message.error(response.message || '头像上传失败')
    }
  } catch (error) {
    message.error('头像上传失败，请稍后重试')
  } finally {
    target.value = ''
    isUploading.value = false
  }
}

// 保存用户资料
const handleSave = async () => {
  if (isSaving.value) return

  isSaving.value = true

  try {
    // 准备提交数据
    const submitData: UpdateProfileRequest = {
      ...formData,
      allergies: JSON.stringify(allergiesList.value),
      dietaryPreferences: JSON.stringify(selectedDietaryPreferences.value),
      healthGoals: JSON.stringify(selectedHealthGoals.value)
    }

    const response = await updateUserProfile(submitData)
    if (response.success && response.data) {
      userProfile.value = response.data

      // 更新全局用户状态
      if (user.value) {
        Object.assign(user.value, response.data)
        UserManager.setUser(user.value)
      }

      message.success('资料保存成功')
      updateUserState()
    } else {
      message.error(response.message || '资料保存失败')
    }
  } catch (error) {
    message.error('资料保存失败，请稍后重试')
  } finally {
    isSaving.value = false
  }
}

onMounted(() => {
  // 检查登录状态
  if (!isLoggedIn.value) {
    router.push('/login')
    return
  }

  // 加载选项数据
  loadDietaryOptions()
  loadHealthGoalOptions()

  // 加载用户资料
  loadUserProfile()
})
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* 全局样式 */
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: -100px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: -75px;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

/* 导航栏现在使用共享组件 TopNavbar */

.save-btn {
  background: #16a085;
  color: white !important;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn:hover:not(:disabled) {
  background: #138d75;
  transform: translateY(-1px);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.profile-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

/* 资料卡片 */
.profile-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--border-color);
}

/* 头像卡片特殊样式 */
.avatar-card {
  grid-column: 1 / -1;
}

.avatar-section {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.avatar-container {
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.avatar-container:hover {
  transform: scale(1.05);
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  text-align: center;
  padding: 0.5rem;
  border-radius: 0 0 50% 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-size: 0.8rem;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.basic-info {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.form-input, .form-select, .form-textarea {
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
}

.form-input.disabled {
  background: #f8f9fa;
  color: var(--text-secondary);
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.char-count {
  font-size: 0.8rem;
  color: var(--text-light);
  text-align: right;
}

/* BMI显示 */
.bmi-display {
  grid-column: 1 / -1;
}

.bmi-value {
  padding: 0.75rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  text-align: center;
}

.bmi-value.underweight {
  background: #e3f2fd;
  color: #1976d2;
}

.bmi-value.normal {
  background: #e8f5e8;
  color: #2e7d32;
}

.bmi-value.overweight {
  background: #fff3e0;
  color: #f57c00;
}

.bmi-value.obese {
  background: #ffebee;
  color: #d32f2f;
}

/* 标签输入样式 */
.tag-input-container {
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0.75rem;
  background: white;
  transition: border-color 0.3s ease;
  min-height: 3rem;
}

.tag-input-container:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  min-height: 2rem;
  align-items: flex-start;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: #16a085 !important;
  color: white !important;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #138d75;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.tag-remove {
  background: none;
  border: none;
  color: white !important;
  cursor: pointer;
  font-size: 1.1rem;
  line-height: 1;
  padding: 0.125rem;
  margin-left: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.1);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 200px;
  padding: 0.375rem;
  font-size: 0.9rem;
  background: transparent;
}

.tag-placeholder {
  color: var(--text-light);
  font-style: italic;
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

/* 复选框组样式 */
.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition: background-color 0.3s ease;
}

.checkbox-item:hover {
  background: rgba(22, 160, 133, 0.05);
}

.checkbox-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.checkbox-label {
  font-size: 0.9rem;
  color: var(--text-primary);
}

/* 健康信息卡片特殊样式 */
.health-card {
  grid-column: 1 / -1;
}

/* 个人简介卡片特殊样式 */
.bio-card {
  grid-column: 1 / -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .profile-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .basic-info {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .checkbox-group {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .profile-card {
    padding: 1rem;
  }

  .save-btn {
    width: 100%;
  }
}
</style>
