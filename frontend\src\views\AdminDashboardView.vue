<template>
  <div class="admin-dashboard">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                  fill="currentColor"/>
            <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h2 class="brand-name">管理后台</h2>
      </div>

      <nav class="sidebar-nav">
        <a
          v-for="item in navigationItems"
          :key="item.key"
          href="#"
          class="nav-item"
          :class="{ active: currentView === item.key }"
          @click="currentView = item.key"
        >
          <span class="nav-icon">{{ item.icon }}</span>
          <span class="nav-text">{{ item.label }}</span>
        </a>
      </nav>

      <div class="sidebar-footer">
        <div class="admin-info">
          <div @click="triggerAvatarUpload" style="cursor: pointer;">
            <AvatarDisplay
              :avatar-url="getAvatarUrl(adminInfo?.avatar)"
              :name="adminInfo?.realName"
              :size="40"
              :clickable="false"
              :show-upload-overlay="true"
            />
          </div>
          <div class="admin-details">
            <div class="admin-name">{{ adminInfo?.realName }}</div>
            <div class="admin-role">{{ adminInfo?.roleName }}</div>
          </div>
        </div>
        <button @click="handleLogout" class="logout-btn">
          <span>退出登录</span>
        </button>
      </div>

      <!-- 隐藏的头像上传文件输入 -->
      <input
        ref="avatarFileInput"
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/gif"
        style="display: none"
        @change="handleAvatarFileSelect"
      />
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="top-header">
        <div class="header-left">
          <h1 class="page-title">{{ getPageTitle() }}</h1>
        </div>
        <div class="header-right">
          <span class="current-time">{{ currentTime }}</span>
        </div>
      </header>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 概览页面 -->
        <div v-if="currentView === 'overview'" class="overview-content">
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">👥</div>
              <div class="stat-info">
                <h3>管理员总数</h3>
                <p class="stat-number">{{ stats.adminCount }}</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">👤</div>
              <div class="stat-info">
                <h3>用户总数</h3>
                <p class="stat-number">{{ userStats.totalUsers }}</p>
                <p class="stat-detail">启用: {{ userStats.activeUsers }} | 禁用: {{ userStats.inactiveUsers }}</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <h3>今日注册</h3>
                <p class="stat-number">{{ userStats.todayRegistered }}</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">🔄</div>
              <div class="stat-info">
                <h3>系统状态</h3>
                <p class="stat-status">{{ stats.systemStatus }}</p>
              </div>
            </div>
          </div>

          <div class="recent-activities">
            <h3>最近活动</h3>
            <div class="activity-list">
              <div v-if="recentActivities.length === 0" class="no-activities">
                <p>暂无最近活动记录</p>
              </div>
              <div v-else v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                <div class="activity-time">{{ formatTime(activity.createTime) }}</div>
                <div class="activity-content">{{ activity.content }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 管理员管理页面 -->
        <div v-else-if="currentView === 'admins'" class="admins-content">
          
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>编号</th>
                  <th>用户名</th>
                  <th>真实姓名</th>
                  <th>邮箱</th>
                  <th>角色</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="admin in adminList" :key="admin.id">
                  <td>{{ admin.id }}</td>
                  <td>{{ admin.username }}</td>
                  <td>{{ admin.realName }}</td>
                  <td>{{ admin.email }}</td>
                  <td>
                    <span class="role-badge">
                      {{ admin.roleName }}
                    </span>
                  </td>
                  <td>
                    <span class="status-badge" :class="admin.status === 1 ? 'active' : 'inactive'">
                      {{ admin.statusName }}
                    </span>
                  </td>
                  <td>{{ formatDate(admin.createTime) }}</td>
                  <td>
                    <div class="action-buttons">
                      <button @click="toggleStatus(admin)" class="action-btn toggle">
                        {{ admin.status === 1 ? '禁用' : '启用' }}
                      </button>
                      <button @click="deleteAdmin(admin)" class="action-btn delete">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 核心功能管理页面 -->
        <div v-else-if="currentView === 'features'" class="features-content">
          <div class="content-header">
            <button @click="showFeatureModal = true" class="create-btn">
              <span>+ 新增核心功能</span>
            </button>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>编号</th>
                  <th>标题</th>
                  <th>描述</th>
                  <th>亮点</th>
                  <th>排序</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="feature in featureList" :key="feature.id">
                  <td>{{ feature.id }}</td>
                  <td>{{ feature.title }}</td>
                  <td class="description-cell">{{ feature.description }}</td>
                  <td>
                    <div class="highlights-cell">
                      <span v-for="highlight in feature.highlights" :key="highlight" class="highlight-tag">
                        {{ highlight }}
                      </span>
                    </div>
                  </td>
                  <td>{{ feature.sortOrder }}</td>
                  <td>
                    <span class="status-badge" :class="feature.status === 1 ? 'active' : 'inactive'">
                      {{ feature.statusName }}
                    </span>
                  </td>
                  <td>{{ formatDate(feature.createTime) }}</td>
                  <td>
                    <div class="action-buttons">
                      <button @click="editFeature(feature)" class="action-btn edit">编辑</button>
                      <button @click="toggleFeatureStatus(feature)" class="action-btn toggle">
                        {{ feature.status === 1 ? '禁用' : '启用' }}
                      </button>
                      <button @click="deleteFeature(feature)" class="action-btn delete">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 用户管理页面 -->
        <div v-else-if="currentView === 'users'" class="users-content">
          <div class="content-header">
            <div class="search-filters">
              <input
                v-model="userSearchForm.username"
                type="text"
                placeholder="搜索用户名..."
                class="search-input"
                @keyup.enter="searchUsers"
              >
              <input
                v-model="userSearchForm.email"
                type="text"
                placeholder="搜索邮箱..."
                class="search-input"
                @keyup.enter="searchUsers"
              >

              <select v-model="userSearchForm.status" class="filter-select" @change="searchUsers">
                <option :value="undefined">全部状态</option>
                <option :value="1">启用</option>
                <option :value="0">禁用</option>
              </select>
              <button @click="searchUsers" class="search-btn">搜索</button>
              <button @click="resetUserSearch" class="reset-btn">重置</button>
            </div>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>编号</th>
                  <th>用户名</th>
                  <th>邮箱</th>
                  <th>状态</th>
                  <th>注册时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="user in userList" :key="user.id">
                  <td>{{ user.id }}</td>
                  <td>{{ user.username }}</td>
                  <td>{{ user.email }}</td>
                  <td>
                    <span class="status-badge" :class="user.status === 1 ? 'active' : 'inactive'">
                      {{ user.statusName || (user.status === 1 ? '启用' : '禁用') }}
                    </span>
                  </td>
                  <td>{{ formatDate(user.createTime) }}</td>
                  <td>
                    <div class="action-buttons">
                      <button @click="toggleUserStatusAction(user)" class="action-btn toggle">
                        {{ user.status === 1 ? '禁用' : '启用' }}
                      </button>
                      <button @click="deleteUserAction(user)" class="action-btn delete">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页组件 -->
          <div class="pagination-container">
            <div class="pagination-info">
              共 {{ userPagination.total }} 条记录，第 {{ userPagination.page }} / {{ Math.ceil(userPagination.total / userPagination.size) }} 页
            </div>
            <div class="pagination-controls">
              <select v-model="userPagination.size" @change="changeUserPageSize(userPagination.size)" class="page-size-select">
                <option :value="10">10条/页</option>
                <option :value="20">20条/页</option>
                <option :value="50">50条/页</option>
              </select>
              <button
                @click="changeUserPage(userPagination.page - 1)"
                :disabled="userPagination.page <= 1"
                class="page-btn"
              >
                上一页
              </button>
              <span class="page-numbers">
                <button
                  v-for="page in getPageNumbers(userPagination.page, Math.ceil(userPagination.total / userPagination.size))"
                  :key="page"
                  @click="changeUserPage(page)"
                  :class="['page-number', { active: page === userPagination.page }]"
                >
                  {{ page }}
                </button>
              </span>
              <button
                @click="changeUserPage(userPagination.page + 1)"
                :disabled="userPagination.page >= Math.ceil(userPagination.total / userPagination.size)"
                class="page-btn"
              >
                下一页
              </button>
            </div>
          </div>
        </div>




        <!-- 饮食偏好管理页面 -->
        <div v-else-if="currentView === 'dietary-preferences'" class="dietary-preferences-content">
          <div class="content-header">
            <button @click="showDietaryPreferenceModal = true" class="create-btn">
              <span>+ 新增饮食偏好</span>
            </button>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>编号</th>
                  <th>代码</th>
                  <th>名称</th>
                  <th>描述</th>
                  <th>排序</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="preference in dietaryPreferenceList" :key="preference.id">
                  <td>{{ preference.id }}</td>
                  <td><code>{{ preference.code }}</code></td>
                  <td>{{ preference.name }}</td>
                  <td>{{ preference.description || '-' }}</td>
                  <td>{{ preference.sortOrder }}</td>
                  <td>
                    <span class="status-badge" :class="preference.status === 1 ? 'active' : 'inactive'">
                      {{ preference.statusName }}
                    </span>
                  </td>
                  <td>{{ formatDate(preference.createTime) }}</td>
                  <td>
                    <div class="action-buttons">
                      <button @click="editDietaryPreference(preference)" class="action-btn edit">编辑</button>
                      <button @click="deleteDietaryPreference(preference)" class="action-btn delete">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 健康目标管理页面 -->
        <div v-else-if="currentView === 'health-goals'" class="health-goals-content">
          <div class="content-header">
            <button @click="showHealthGoalModal = true" class="create-btn">
              <span>+ 新增健康目标</span>
            </button>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>编号</th>
                  <th>代码</th>
                  <th>名称</th>
                  <th>描述</th>
                  <th>排序</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="goal in healthGoalList" :key="goal.id">
                  <td>{{ goal.id }}</td>
                  <td><code>{{ goal.code }}</code></td>
                  <td>{{ goal.name }}</td>
                  <td>{{ goal.description || '-' }}</td>
                  <td>{{ goal.sortOrder }}</td>
                  <td>
                    <span class="status-badge" :class="goal.status === 1 ? 'active' : 'inactive'">
                      {{ goal.statusName }}
                    </span>
                  </td>
                  <td>{{ formatDate(goal.createTime) }}</td>
                  <td>
                    <div class="action-buttons">
                      <button @click="editHealthGoal(goal)" class="action-btn edit">编辑</button>
                      <button @click="deleteHealthGoal(goal)" class="action-btn delete">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 其他页面占位 -->
        <div v-else class="placeholder-content">
          <div class="placeholder-icon">🚧</div>
          <h3>功能开发中</h3>
          <p>{{ getPageTitle() }}功能正在开发中，敬请期待！</p>
        </div>
      </div>
    </main>



    <!-- 核心功能模态框 -->
    <div v-if="showFeatureModal" class="modal-overlay" @click="closeFeatureModal">
      <div class="modal-content feature-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ editingFeature ? '编辑核心功能' : '新增核心功能' }}</h3>
          <button @click="closeFeatureModal" class="close-btn">×</button>
        </div>
        <form @submit.prevent="submitFeature" class="modal-form">
          <div class="form-group">
            <label>功能标题</label>
            <input v-model="featureForm.title" type="text" required>
          </div>
          <div class="form-group">
            <label>功能描述</label>
            <textarea v-model="featureForm.description" rows="3" required></textarea>
          </div>
          <div class="form-group">
            <label>功能图标 (SVG)</label>
            <textarea v-model="featureForm.icon" rows="4" placeholder="请输入SVG图标代码" required></textarea>
          </div>
          <div class="form-group">
            <label>功能亮点 (每行一个)</label>
            <textarea v-model="highlightsText" rows="3" placeholder="请输入功能亮点，每行一个" required></textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>排序顺序</label>
              <input v-model.number="featureForm.sortOrder" type="number" min="1">
            </div>
            <div class="form-group">
              <label>状态</label>
              <select v-model="featureForm.status" required>
                <option v-for="status in statusOptions" :key="status.value" :value="status.value">
                  {{ status.label }}
                </option>
              </select>
            </div>
          </div>
          <div class="modal-actions">
            <button type="button" @click="closeFeatureModal" class="cancel-btn">取消</button>
            <button type="submit" class="submit-btn" :disabled="isSubmittingFeature">
              {{ isSubmittingFeature ? '提交中...' : (editingFeature ? '更新' : '创建') }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 健康目标模态框 -->
    <div v-if="showHealthGoalModal" class="modal-overlay" @click="closeHealthGoalModal">
      <div class="modal-content health-goal-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ editingHealthGoal ? '编辑健康目标' : '新增健康目标' }}</h3>
          <button @click="closeHealthGoalModal" class="close-btn">×</button>
        </div>
        <form @submit.prevent="submitHealthGoal" class="modal-form">
          <div class="form-group">
            <label>目标代码</label>
            <input v-model="healthGoalForm.code" type="text" required placeholder="如：weight-loss">
          </div>
          <div class="form-group">
            <label>目标名称</label>
            <input v-model="healthGoalForm.name" type="text" required placeholder="如：减重">
          </div>
          <div class="form-group">
            <label>目标描述</label>
            <textarea v-model="healthGoalForm.description" rows="3" placeholder="请输入目标描述"></textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>排序顺序</label>
              <input v-model.number="healthGoalForm.sortOrder" type="number" min="1" required>
            </div>
            <div class="form-group">
              <label>状态</label>
              <select v-model.number="healthGoalForm.status" required>
                <option :value="1">启用</option>
                <option :value="0">禁用</option>
              </select>
            </div>
          </div>
          <div class="modal-actions">
            <button type="button" @click="closeHealthGoalModal" class="cancel-btn">取消</button>
            <button type="submit" class="submit-btn" :disabled="isSubmittingHealthGoal">
              {{ isSubmittingHealthGoal ? '提交中...' : (editingHealthGoal ? '更新' : '创建') }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 饮食偏好模态框 -->
    <div v-if="showDietaryPreferenceModal" class="modal-overlay" @click="closeDietaryPreferenceModal">
      <div class="modal-content dietary-preference-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ editingDietaryPreference ? '编辑饮食偏好' : '新增饮食偏好' }}</h3>
          <button @click="closeDietaryPreferenceModal" class="close-btn">×</button>
        </div>
        <form @submit.prevent="submitDietaryPreference" class="modal-form">
          <div class="form-group">
            <label>偏好代码</label>
            <input v-model="dietaryPreferenceForm.code" type="text" required placeholder="如：vegetarian">
          </div>
          <div class="form-group">
            <label>偏好名称</label>
            <input v-model="dietaryPreferenceForm.name" type="text" required placeholder="如：素食主义">
          </div>
          <div class="form-group">
            <label>偏好描述</label>
            <textarea v-model="dietaryPreferenceForm.description" rows="3" placeholder="请输入偏好描述"></textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>排序顺序</label>
              <input v-model.number="dietaryPreferenceForm.sortOrder" type="number" min="1" required>
            </div>
            <div class="form-group">
              <label>状态</label>
              <select v-model.number="dietaryPreferenceForm.status" required>
                <option :value="1">启用</option>
                <option :value="0">禁用</option>
              </select>
            </div>
          </div>
          <div class="modal-actions">
            <button type="button" @click="closeDietaryPreferenceModal" class="cancel-btn">取消</button>
            <button type="submit" class="submit-btn" :disabled="isSubmittingDietaryPreference">
              {{ isSubmittingDietaryPreference ? '提交中...' : (editingDietaryPreference ? '更新' : '创建') }}
            </button>
          </div>
        </form>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { getAdminList, deleteAdmin as deleteAdminApi, toggleAdminStatus, type Admin, getUserList, toggleUserStatus, deleteUser as deleteUserApi, getUserStats, type User, type UserStatsResponse, getAvatarUrl } from '../utils/adminApi'
import { getCoreFeatureList, createCoreFeature, updateCoreFeature, deleteCoreFeature, updateCoreFeatureStatus, type CoreFeature, type CoreFeatureRequest } from '../utils/coreFeatureApi'
import AvatarDisplay from '../components/AvatarDisplay.vue'
import { message } from '../utils/message'

import { AuthManager, TokenManager } from '../utils/auth'

const router = useRouter()

// 响应式数据
const currentView = ref('overview')
const currentTime = ref('')
const adminInfo = ref<Admin | null>(null)
const adminList = ref<Admin[]>([])

// 核心功能管理相关数据
const featureList = ref<CoreFeature[]>([])
const showFeatureModal = ref(false)
const editingFeature = ref<CoreFeature | null>(null)
const isSubmittingFeature = ref(false)
const highlightsText = ref('')



// 用户管理相关数据
const userList = ref<User[]>([])
const userStats = ref<UserStatsResponse>({
  totalUsers: 0,
  activeUsers: 0,
  inactiveUsers: 0,
  todayRegistered: 0
})

// 用户搜索和分页
const userSearchForm = reactive({
  username: '',
  email: '',
  status: undefined as number | undefined
})

const userPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 最近活动数据
interface Activity {
  id: number
  content: string
  createTime: string
}
const recentActivities = ref<Activity[]>([])

// 状态选项
const statusOptions = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
]

// 导航菜单项
const navigationItems = [
  { key: 'overview', icon: '📊', label: '概览' },
  { key: 'admins', icon: '👥', label: '管理员管理' },
  { key: 'users', icon: '👤', label: '用户管理' },
  { key: 'features', icon: '⭐', label: '核心功能管理' },
  { key: 'dietary-preferences', icon: '🥗', label: '饮食偏好管理' },
  { key: 'health-goals', icon: '🎯', label: '健康目标管理' }
]



const stats = reactive({
  adminCount: 0,
  userCount: 0,
  activeToday: 0,
  systemStatus: '正常运行'
})



const featureForm = reactive({
  title: '',
  description: '',
  icon: '',
  highlights: [] as string[],
  sortOrder: 1,
  status: 1
})

// 健康目标管理相关状态
interface HealthGoal {
  id: number
  code: string
  name: string
  description?: string
  sortOrder: number
  status: number
  statusName: string
  createTime: string
  updateTime: string
}

const healthGoalList = ref<HealthGoal[]>([])
const showHealthGoalModal = ref(false)
const editingHealthGoal = ref<HealthGoal | null>(null)
const isSubmittingHealthGoal = ref(false)

const healthGoalForm = reactive({
  code: '',
  name: '',
  description: '',
  sortOrder: 1,
  status: 1
})

// 饮食偏好管理相关状态
interface DietaryPreference {
  id: number
  code: string
  name: string
  description?: string
  sortOrder: number
  status: number
  statusName: string
  createTime: string
  updateTime: string
}

const dietaryPreferenceList = ref<DietaryPreference[]>([])
const showDietaryPreferenceModal = ref(false)
const editingDietaryPreference = ref<DietaryPreference | null>(null)
const isSubmittingDietaryPreference = ref(false)

const dietaryPreferenceForm = reactive({
  code: '',
  name: '',
  description: '',
  sortOrder: 1,
  status: 1
})



// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN')
}

const getPageTitle = () => {
  const titles: Record<string, string> = {
    overview: '系统概览',
    admins: '管理员管理',
    users: '用户管理',
    features: '核心功能管理',
    'dietary-preferences': '饮食偏好管理',
    'health-goals': '健康目标管理'
  }
  return titles[currentView.value] || '未知页面'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const loadAdminInfo = () => {
  const userData = AuthManager.getCurrentUser()
  if (userData && userData.userType === 'ADMIN') {
    adminInfo.value = userData
  } else {
    // 不是管理员或未登录，跳转到登录页
    router.push('/admin/login')
  }
}

const loadAdminList = async () => {
  try {
    const response = await getAdminList({ page: 1, size: 100 })
    if (response.success && response.data) {
      adminList.value = response.data.records
      stats.adminCount = response.data.total
    }
  } catch (error) {
    // 加载管理员列表失败时静默处理
  }
}

const loadFeatureList = async () => {
  try {
    const response = await getCoreFeatureList({ page: 1, size: 100 })
    if (response.success && response.data) {
      featureList.value = response.data.records
    }
  } catch (error) {
    // 加载核心功能列表失败时静默处理
  }
}

const loadUserStats = async () => {
  try {
    const response = await getUserStats()
    if (response.success && response.data) {
      userStats.value = response.data
      stats.userCount = response.data.totalUsers
      stats.activeToday = response.data.todayRegistered
    }
  } catch (error) {
    // 加载用户统计失败时静默处理
  }
}

const loadRecentActivities = async () => {
  try {
    // TODO: 实现活动日志API后替换
    // 暂时清空活动列表
    recentActivities.value = []
  } catch (error) {
    // 加载最近活动失败时静默处理
  }
}



const toggleStatus = async (admin: Admin) => {
  try {
    const newStatus = admin.status === 1 ? 0 : 1
    await toggleAdminStatus(admin.id, newStatus)
    loadAdminList()
  } catch (error) {
    // 状态切换失败时静默处理
  }
}

const deleteAdmin = async (admin: Admin) => {
  if (confirm(`确定要删除管理员 ${admin.realName} 吗？`)) {
    try {
      await deleteAdminApi(admin.id)
      loadAdminList()
    } catch (error) {
      // 删除失败时静默处理
    }
  }
}

const handleLogout = () => {
  // 使用AuthManager进行正确的登出
  AuthManager.logout()
  router.push('/admin/login')
}

// 核心功能管理方法
const editFeature = (feature: CoreFeature) => {
  editingFeature.value = feature
  featureForm.title = feature.title
  featureForm.description = feature.description
  featureForm.icon = feature.icon
  featureForm.highlights = [...feature.highlights]
  featureForm.sortOrder = feature.sortOrder
  featureForm.status = feature.status
  highlightsText.value = feature.highlights.join('\n')
  showFeatureModal.value = true
}

const closeFeatureModal = () => {
  showFeatureModal.value = false
  editingFeature.value = null
  Object.assign(featureForm, {
    title: '',
    description: '',
    icon: '',
    highlights: [],
    sortOrder: 1,
    status: 1
  })
  highlightsText.value = ''
}

const submitFeature = async () => {
  isSubmittingFeature.value = true
  try {
    // 处理亮点数据
    featureForm.highlights = highlightsText.value.split('\n').filter(h => h.trim())

    const data: CoreFeatureRequest = {
      title: featureForm.title,
      description: featureForm.description,
      icon: featureForm.icon,
      highlights: featureForm.highlights,
      sortOrder: featureForm.sortOrder,
      status: featureForm.status
    }

    if (editingFeature.value) {
      await updateCoreFeature(editingFeature.value.id, data)
    } else {
      await createCoreFeature(data)
    }

    closeFeatureModal()
    loadFeatureList()
  } catch (error) {
    // 操作失败时静默处理
  } finally {
    isSubmittingFeature.value = false
  }
}

const toggleFeatureStatus = async (feature: CoreFeature) => {
  try {
    const newStatus = feature.status === 1 ? 0 : 1
    await updateCoreFeatureStatus(feature.id, newStatus)
    loadFeatureList()
  } catch (error) {
    // 状态切换失败时静默处理
  }
}

const deleteFeature = async (feature: CoreFeature) => {
  if (confirm(`确定要删除核心功能 ${feature.title} 吗？`)) {
    try {
      await deleteCoreFeature(feature.id)
      loadFeatureList()
    } catch (error) {
      // 删除失败时静默处理
    }
  }
}

// ==================== 用户管理方法 ====================

const loadUserList = async () => {
  try {
    const params = {
      page: userPagination.page,
      size: userPagination.size,
      username: userSearchForm.username || undefined,
      email: userSearchForm.email || undefined,
      status: userSearchForm.status
    }

    const response = await getUserList(params)
    if (response.success && response.data) {
      userList.value = response.data.records
      userPagination.total = response.data.total
    }
  } catch (error) {
    // 加载用户列表失败时静默处理
  }
}

const toggleUserStatusAction = async (user: User) => {
  try {
    const newStatus = user.status === 1 ? 0 : 1
    await toggleUserStatus(user.id, newStatus)
    loadUserList()
    loadUserStats() // 重新加载统计信息
  } catch (error) {
    // 状态切换失败时静默处理
  }
}

const deleteUserAction = async (user: User) => {
  if (confirm(`确定要删除用户 ${user.username} 吗？`)) {
    try {
      await deleteUserApi(user.id)
      loadUserList()
      loadUserStats() // 重新加载统计信息
    } catch (error) {
      // 删除失败时静默处理
    }
  }
}

// 搜索用户
const searchUsers = () => {
  userPagination.page = 1 // 重置到第一页
  loadUserList()
}

// 重置搜索
const resetUserSearch = () => {
  Object.assign(userSearchForm, {
    username: '',
    email: '',
    status: undefined
  })
  userPagination.page = 1
  loadUserList()
}

// 分页相关方法
const changeUserPage = (page: number) => {
  userPagination.page = page
  loadUserList()
}

const changeUserPageSize = (size: number) => {
  userPagination.size = size
  userPagination.page = 1
  loadUserList()
}

// 获取分页页码数组
const getPageNumbers = (currentPage: number, totalPages: number) => {
  const pages = []
  const maxVisible = 5 // 最多显示5个页码

  let start = Math.max(1, currentPage - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages, start + maxVisible - 1)

  // 调整起始位置
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

// ==================== 头像管理方法 ====================

const avatarFileInput = ref<HTMLInputElement>()
const isUploading = ref(false)

const triggerAvatarUpload = () => {
  // 防止重复触发
  if (isUploading.value) {
    return
  }

  // 触发文件选择
  if (avatarFileInput.value) {
    avatarFileInput.value.click()
  }
}

const handleAvatarFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  // 如果没有选择文件或正在上传，直接返回
  if (!file || isUploading.value) return

  // 验证文件
  if (!validateAvatarFile(file)) {
    return
  }

  isUploading.value = true

  try {
    // 上传头像
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('http://localhost:8080/api/files/avatar/admin', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TokenManager.getToken()}`
      },
      body: formData
    })

    const result = await response.json()

    if (result.success) {
      // 更新头像路径
      if (adminInfo.value) {
        adminInfo.value.avatar = result.data
        // 同时更新localStorage中的管理员信息
        const { UserManager } = await import('../utils/auth')
        UserManager.setUser(adminInfo.value)
      }
      message.success('头像上传成功')
    } else {
      message.error(result.message || '头像上传失败')
    }
  } catch (error) {
    message.error('头像上传失败，请重试')
  } finally {
    isUploading.value = false
    // 清空文件输入
    target.value = ''
  }
}

const validateAvatarFile = (file: File): boolean => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、GIF 格式的图片')
    return false
  }

  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过 10MB')
    return false
  }

  return true
}


// 生命周期
onMounted(() => {
  loadAdminInfo()
  loadAdminList()
  loadFeatureList()
  loadUserList()
  loadUserStats()
  loadRecentActivities()

  updateTime()
  const timer = setInterval(updateTime, 1000)

  onUnmounted(() => {
    clearInterval(timer)
  })
})

// 健康目标管理方法
const loadHealthGoals = async () => {
  try {
    const token = TokenManager.getToken()
    console.log('Token:', token ? 'exists' : 'missing')

    const response = await fetch('http://localhost:8080/api/health-goals/admin/all', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    console.log('Response status:', response.status)
    console.log('Response headers:', response.headers.get('content-type'))

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API Error:', errorText)
      return
    }

    const result = await response.json()
    if (result.success) {
      healthGoalList.value = result.data
    } else {
      console.error('API返回错误:', result.message)
    }
  } catch (error) {
    console.error('加载健康目标失败:', error)
  }
}

const editHealthGoal = (goal: any) => {
  editingHealthGoal.value = goal
  healthGoalForm.code = goal.code
  healthGoalForm.name = goal.name
  healthGoalForm.description = goal.description || ''
  healthGoalForm.sortOrder = goal.sortOrder
  healthGoalForm.status = goal.status
  showHealthGoalModal.value = true
}

const deleteHealthGoal = async (goal: any) => {
  if (confirm(`确定要删除健康目标 "${goal.name}" 吗？`)) {
    try {
      const response = await fetch(`http://localhost:8080/api/health-goals/admin/${goal.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${TokenManager.getToken()}`
        }
      })
      const result = await response.json()
      if (result.success) {
        loadHealthGoals()
      }
    } catch (error) {
      console.error('删除健康目标失败:', error)
    }
  }
}

const closeHealthGoalModal = () => {
  showHealthGoalModal.value = false
  editingHealthGoal.value = null
  Object.assign(healthGoalForm, {
    code: '',
    name: '',
    description: '',
    sortOrder: 1,
    status: 1
  })
}

const submitHealthGoal = async () => {
  if (isSubmittingHealthGoal.value) return

  isSubmittingHealthGoal.value = true
  try {
    const url = editingHealthGoal.value
      ? `http://localhost:8080/api/health-goals/admin/${editingHealthGoal.value.id}`
      : 'http://localhost:8080/api/health-goals/admin/create'

    const method = editingHealthGoal.value ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TokenManager.getToken()}`
      },
      body: JSON.stringify(healthGoalForm)
    })

    const result = await response.json()
    if (result.success) {
      closeHealthGoalModal()
      loadHealthGoals()
    }
  } catch (error) {
    console.error('保存健康目标失败:', error)
  } finally {
    isSubmittingHealthGoal.value = false
  }
}

// 饮食偏好管理方法
const loadDietaryPreferences = async () => {
  try {
    const token = TokenManager.getToken()
    console.log('Token:', token ? 'exists' : 'missing')

    const response = await fetch('http://localhost:8080/api/dietary-preferences/admin/all', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    console.log('Response status:', response.status)
    console.log('Response headers:', response.headers.get('content-type'))

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API Error:', errorText)
      return
    }

    const result = await response.json()
    if (result.success) {
      dietaryPreferenceList.value = result.data
    } else {
      console.error('API返回错误:', result.message)
    }
  } catch (error) {
    console.error('加载饮食偏好失败:', error)
  }
}

const editDietaryPreference = (preference: DietaryPreference) => {
  editingDietaryPreference.value = preference
  dietaryPreferenceForm.code = preference.code
  dietaryPreferenceForm.name = preference.name
  dietaryPreferenceForm.description = preference.description || ''
  dietaryPreferenceForm.sortOrder = preference.sortOrder
  dietaryPreferenceForm.status = preference.status
  showDietaryPreferenceModal.value = true
}

const deleteDietaryPreference = async (preference: DietaryPreference) => {
  if (confirm(`确定要删除饮食偏好 "${preference.name}" 吗？`)) {
    try {
      const response = await fetch(`http://localhost:8080/api/dietary-preferences/admin/${preference.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${TokenManager.getToken()}`
        }
      })
      const result = await response.json()
      if (result.success) {
        loadDietaryPreferences()
      }
    } catch (error) {
      console.error('删除饮食偏好失败:', error)
    }
  }
}

const closeDietaryPreferenceModal = () => {
  showDietaryPreferenceModal.value = false
  editingDietaryPreference.value = null
  Object.assign(dietaryPreferenceForm, {
    code: '',
    name: '',
    description: '',
    sortOrder: 1,
    status: 1
  })
}

const submitDietaryPreference = async () => {
  if (isSubmittingDietaryPreference.value) return

  isSubmittingDietaryPreference.value = true
  try {
    const url = editingDietaryPreference.value
      ? `http://localhost:8080/api/dietary-preferences/admin/${editingDietaryPreference.value.id}`
      : 'http://localhost:8080/api/dietary-preferences/admin/create'

    const method = editingDietaryPreference.value ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TokenManager.getToken()}`
      },
      body: JSON.stringify(dietaryPreferenceForm)
    })

    const result = await response.json()
    if (result.success) {
      closeDietaryPreferenceModal()
      loadDietaryPreferences()
    }
  } catch (error) {
    console.error('保存饮食偏好失败:', error)
  } finally {
    isSubmittingDietaryPreference.value = false
  }
}

// 监听当前视图变化，加载对应数据
watch(currentView, (newView) => {
  if (newView === 'health-goals') {
    loadHealthGoals()
  } else if (newView === 'dietary-preferences') {
    loadDietaryPreferences()
  }
})
</script>

<style scoped>
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background: #f7fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 侧边栏样式 */
.sidebar {
  width: 260px;
  background: #2d3748;
  color: white;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid #4a5568;
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo svg {
  width: 24px;
  height: 24px;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #cbd5e0;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-item:hover {
  background: #4a5568;
  color: white;
}

.nav-item.active {
  background: #667eea;
  color: white;
}

.nav-icon {
  font-size: 1.2rem;
}

.nav-text {
  font-weight: 500;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #4a5568;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}



.admin-details {
  flex: 1;
}

.admin-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.admin-role {
  font-size: 0.8rem;
  color: #a0aec0;
}

.logout-btn {
  width: 100%;
  padding: 8px 16px;
  background: #e53e3e;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.logout-btn:hover {
  background: #c53030;
}

/* 主内容区样式 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.top-header {
  background: white;
  padding: 20px 32px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.current-time {
  color: #718096;
  font-size: 0.9rem;
}

.content-area {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

/* 概览页面样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  background: #f7fafc;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info h3 {
  font-size: 0.9rem;
  color: #718096;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.stat-status {
  font-size: 1rem;
  font-weight: 600;
  color: #38a169;
  margin: 0;
}

.recent-activities {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recent-activities h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f7fafc;
}

.activity-time {
  color: #718096;
  font-size: 0.9rem;
  min-width: 60px;
}

.activity-content {
  color: #4a5568;
}

.no-activities {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
}

.no-activities p {
  margin: 0;
  font-size: 0.9rem;
}

/* 管理员管理页面样式 */
.content-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.create-btn:hover {
  background: #5a67d8;
}

.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f7fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
}

.data-table td {
  padding: 16px;
  border-bottom: 1px solid #f7fafc;
}

.role-badge, .status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.role-badge {
  background: #bee3f8;
  color: #2b6cb0;
}

.status-badge.active {
  background: #c6f6d5;
  color: #276749;
}

.status-badge.inactive {
  background: #fed7d7;
  color: #c53030;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background: #bee3f8;
  color: #2b6cb0;
}

.action-btn.toggle {
  background: #faf089;
  color: #744210;
}

.action-btn.delete {
  background: #fed7d7;
  color: #c53030;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* 核心功能管理样式 */
.description-cell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.highlights-cell {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 150px;
}

.highlight-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  white-space: nowrap;
}

.feature-modal {
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.feature-modal textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  resize: vertical;
  font-family: inherit;
}

.feature-modal textarea:focus {
  outline: none;
  border-color: #667eea;
}

/* 用户管理页面样式 */
.users-content .content-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  min-width: 200px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.filter-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn {
  background: #667eea;
  color: white;
}

.search-btn:hover {
  background: #5a67d8;
}

.reset-btn {
  background: #e2e8f0;
  color: #4a5568;
}

.reset-btn:hover {
  background: #cbd5e0;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #f7fafc;
}

.pagination-info {
  color: #718096;
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-size-select {
  padding: 6px 10px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.9rem;
  background: white;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  padding: 6px 10px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 36px;
  text-align: center;
}

.page-number:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.page-number.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 统计卡片详情样式 */
.stat-detail {
  font-size: 0.8rem;
  color: #718096;
  margin: 4px 0 0 0;
}

/* 占位内容样式 */
.placeholder-content {
  text-align: center;
  padding: 80px 20px;
  color: #718096;
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 16px;
}

.placeholder-content h3 {
  margin: 0 0 8px 0;
  color: #4a5568;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #2d3748;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #718096;
}

.modal-form {
  padding: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #4a5568;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.cancel-btn {
  padding: 12px 20px;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.submit-btn {
  padding: 12px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .admin-dashboard {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }

  .settings-tabs {
    flex-direction: column;
    gap: 8px;
  }

  .tab-button {
    justify-content: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .system-actions {
    flex-direction: column;
    gap: 12px;
  }
}




</style>
